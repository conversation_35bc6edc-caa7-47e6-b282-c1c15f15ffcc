// Room Homepage JavaScript
class RoomHomepage {
  constructor() {
    this.currentSlide = 1;
    this.totalSlides = 3;
    this.isAnimating = false;
    
    this.init();
  }
  
  init() {
    this.bindEvents();
    this.setupKeyboardNavigation();
  }
  
  bindEvents() {
    // Mobile navigation toggle
    const navToggle = document.querySelector('.nav__toggle');
    const navList = document.querySelector('.nav__list');
    
    if (navToggle && navList) {
      navToggle.addEventListener('click', () => {
        navToggle.classList.toggle('active');
        navList.classList.toggle('active');
        
        // Prevent body scroll when menu is open
        document.body.style.overflow = navList.classList.contains('active') ? 'hidden' : '';
      });
      
      // Close menu when clicking on nav links
      const navLinks = document.querySelectorAll('.nav__link');
      navLinks.forEach(link => {
        link.addEventListener('click', () => {
          navToggle.classList.remove('active');
          navList.classList.remove('active');
          document.body.style.overflow = '';
        });
      });
    }
    
    // Slider controls
    const prevBtn = document.querySelector('.hero__btn--prev');
    const nextBtn = document.querySelector('.hero__btn--next');
    
    if (prevBtn && nextBtn) {
      prevBtn.addEventListener('click', () => this.previousSlide());
      nextBtn.addEventListener('click', () => this.nextSlide());
    }
    
    // Close mobile menu when clicking outside
    document.addEventListener('click', (e) => {
      if (navList && navList.classList.contains('active')) {
        if (!navToggle.contains(e.target) && !navList.contains(e.target)) {
          navToggle.classList.remove('active');
          navList.classList.remove('active');
          document.body.style.overflow = '';
        }
      }
    });
  }
  
  setupKeyboardNavigation() {
    document.addEventListener('keydown', (e) => {
      // Only handle keyboard navigation when not typing in an input
      if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
        return;
      }
      
      switch (e.key) {
        case 'ArrowLeft':
          e.preventDefault();
          this.previousSlide();
          break;
        case 'ArrowRight':
          e.preventDefault();
          this.nextSlide();
          break;
        case 'Escape':
          // Close mobile menu with Escape key
          const navToggle = document.querySelector('.nav__toggle');
          const navList = document.querySelector('.nav__list');
          if (navList && navList.classList.contains('active')) {
            navToggle.classList.remove('active');
            navList.classList.remove('active');
            document.body.style.overflow = '';
          }
          break;
      }
    });
  }
  
  nextSlide() {
    if (this.isAnimating) return;
    
    this.currentSlide = this.currentSlide >= this.totalSlides ? 1 : this.currentSlide + 1;
    this.updateSlide();
  }
  
  previousSlide() {
    if (this.isAnimating) return;
    
    this.currentSlide = this.currentSlide <= 1 ? this.totalSlides : this.currentSlide - 1;
    this.updateSlide();
  }
  
  updateSlide() {
    this.isAnimating = true;
    
    // Remove active class from all slides
    const slides = document.querySelectorAll('.hero__slide');
    slides.forEach(slide => {
      slide.classList.remove('hero__slide--active');
    });
    
    // Add active class to current slide
    const currentSlideElement = document.querySelector(`[data-slide="${this.currentSlide}"]`);
    if (currentSlideElement) {
      currentSlideElement.classList.add('hero__slide--active');
    }
    
    // Reset animation flag after transition
    setTimeout(() => {
      this.isAnimating = false;
    }, 300);
  }
  
  // Auto-play functionality (optional)
  startAutoPlay(interval = 5000) {
    this.autoPlayInterval = setInterval(() => {
      this.nextSlide();
    }, interval);
  }
  
  stopAutoPlay() {
    if (this.autoPlayInterval) {
      clearInterval(this.autoPlayInterval);
    }
  }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  const homepage = new RoomHomepage();
  
  // Optional: Start auto-play (uncomment if desired)
  // homepage.startAutoPlay();
  
  // Pause auto-play on user interaction
  const interactiveElements = document.querySelectorAll('.hero__btn, .nav__toggle, .nav__link');
  interactiveElements.forEach(element => {
    element.addEventListener('click', () => {
      homepage.stopAutoPlay();
    });
  });
});

// Handle window resize
window.addEventListener('resize', () => {
  const navList = document.querySelector('.nav__list');
  const navToggle = document.querySelector('.nav__toggle');
  
  // Close mobile menu on resize to desktop
  if (window.innerWidth >= 768 && navList && navList.classList.contains('active')) {
    navToggle.classList.remove('active');
    navList.classList.remove('active');
    document.body.style.overflow = '';
  }
});

// Smooth scrolling for anchor links (if any are added)
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
  anchor.addEventListener('click', function (e) {
    e.preventDefault();
    const target = document.querySelector(this.getAttribute('href'));
    if (target) {
      target.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  });
});
