/* CSS Reset and Base Styles */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

:root {
  /* Colors */
  --white: hsl(0, 100%, 100%);
  --grey-500: hsl(0, 0%, 63%);
  --grey-800: hsl(0, 0%, 27%);
  --black: hsl(0, 0%, 0%);
  
  /* Typography */
  --font-family: 'League Spartan', sans-serif;
  --font-size-base: 16px;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* Layout */
  --mobile-width: 375px;
  --desktop-width: 1440px;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  line-height: 1.6;
  color: var(--grey-800);
  overflow-x: hidden;
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

button {
  border: none;
  background: none;
  cursor: pointer;
  font-family: inherit;
}

a {
  text-decoration: none;
  color: inherit;
}

ul {
  list-style: none;
}

/* Header and Navigation */
.header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  padding: 3rem 1.5rem 0;
}

.nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.nav__toggle {
  display: block;
  z-index: 200;
}

.nav__close {
  display: none;
}

.nav__toggle.active .nav__hamburger {
  display: none;
}

.nav__toggle.active .nav__close {
  display: block;
}

.nav__logo {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.nav__list {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2rem;
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  z-index: 150;
}

.nav__list.active {
  transform: translateX(0);
}

.nav__link {
  font-weight: var(--font-weight-medium);
  text-transform: lowercase;
  position: relative;
  transition: color 0.3s ease;
  color: var(--black);
}

.nav__link:hover {
  color: var(--grey-500);
}

.nav__link::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 2px;
  background: var(--black);
  transition: width 0.3s ease;
}

.nav__link:hover::after {
  width: 100%;
}

/* Hero Section */
.hero {
  position: relative;
}

.hero__slider {
  position: relative;
}

.hero__slide {
  display: none;
}

.hero__slide--active {
  display: block;
}

.hero__image {
  height: 100vh;
  overflow: hidden;
  position: relative;
}

.hero__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero__content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 4rem 2rem 6rem;
  background: var(--white);
}

.hero__title {
  font-size: 2.5rem;
  font-weight: var(--font-weight-bold);
  line-height: 1;
  margin-bottom: 1.5rem;
  color: var(--black);
}

.hero__text {
  color: var(--grey-500);
  margin-bottom: 2.5rem;
  line-height: 1.5;
  font-size: 0.9rem;
}

.hero__cta {
  display: inline-flex;
  align-items: center;
  gap: 2rem;
  font-weight: var(--font-weight-semibold);
  text-transform: uppercase;
  letter-spacing: 0.75rem;
  color: var(--black);
  transition: color 0.3s ease;
  font-size: 0.9rem;
}

.hero__cta:hover {
  color: var(--grey-500);
}

.hero__arrow {
  transition: transform 0.3s ease;
}

.hero__cta:hover .hero__arrow {
  transform: translateX(0.5rem);
}

/* Hero Controls */
.hero__controls {
  position: absolute;
  bottom: 6rem;
  right: 2rem;
  display: flex;
}

.hero__btn {
  width: 3.5rem;
  height: 3.5rem;
  background: var(--black);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease;
}

.hero__btn:hover {
  background: var(--grey-800);
}

.hero__btn:focus {
  outline: 2px solid var(--grey-500);
  outline-offset: 2px;
}

/* About Section */
.about {
  display: grid;
  grid-template-columns: 1fr;
}

.about__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.about__content {
  padding: 3rem 2rem;
  background: var(--white);
}

.about__title {
  font-size: 1rem;
  font-weight: var(--font-weight-bold);
  text-transform: uppercase;
  letter-spacing: 0.3rem;
  margin-bottom: 1.5rem;
  color: var(--black);
}

.about__text {
  color: var(--grey-500);
  line-height: 1.7;
}

/* Attribution */
.attribution {
  font-size: 11px;
  text-align: center;
  padding: 1rem;
  background: var(--white);
}

.attribution a {
  color: hsl(228, 45%, 44%);
}

/* Utility Classes */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Media Queries - Tablet and Desktop */
@media (min-width: 768px) {
  /* Header */
  .header {
    padding: 4rem 4rem 0;
  }

  .nav {
    justify-content: flex-start;
    gap: 3.5rem;
  }

  .nav__toggle {
    display: none;
  }

  .nav__logo {
    position: static;
    transform: none;
    order: -1;
  }

  .nav__list {
    position: static;
    background: transparent;
    transform: none;
    gap: 2rem;
    justify-content: flex-start;
  }

  .nav__link {
    color: var(--white);
    font-weight: var(--font-weight-medium);
  }

  .nav__link:hover {
    color: var(--white);
    opacity: 0.7;
  }

  .nav__link::after {
    background: var(--white);
  }

  /* Hero Section */
  .hero__slide {
    display: grid;
    grid-template-columns: 3fr 2fr;
    height: 70vh;
  }

  .hero__slide--active {
    display: grid;
  }

  .hero__image {
    height: 100%;
  }

  .hero__content {
    position: static;
    padding: 8rem 6rem 8rem 4rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    background: var(--white);
  }

  .hero__title {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    line-height: 0.9;
  }

  .hero__text {
    margin-bottom: 2rem;
    font-size: 0.875rem;
    line-height: 1.6;
  }

  .hero__cta {
    font-size: 0.875rem;
    letter-spacing: 0.8rem;
  }

  .hero__controls {
    position: absolute;
    bottom: 0;
    right: calc(40% - 5rem);
    z-index: 10;
  }

  .hero__btn {
    width: 5rem;
    height: 5rem;
  }

  /* About Section */
  .about {
    grid-template-columns: 1fr 2fr 1fr;
    height: 30vh;
  }

  .about__content {
    padding: 3rem 4rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .about__title {
    font-size: 0.875rem;
    margin-bottom: 1rem;
    letter-spacing: 0.3rem;
  }

  .about__text {
    font-size: 0.875rem;
    line-height: 1.6;
  }
}

@media (min-width: 1200px) {
  .hero__content {
    padding: 10rem 8rem 10rem 6rem;
  }

  .hero__title {
    font-size: 3rem;
    margin-bottom: 2rem;
  }

  .hero__text {
    font-size: 1rem;
    margin-bottom: 2.5rem;
  }

  .hero__cta {
    font-size: 1rem;
  }

  .about__content {
    padding: 4rem 6rem;
  }

  .about__title {
    font-size: 1rem;
  }

  .about__text {
    font-size: 1rem;
  }
}
